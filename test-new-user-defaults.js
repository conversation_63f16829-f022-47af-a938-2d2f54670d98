/**
 * Test script to verify new user gets all 5 default custom fields
 * Tests: Priority, Status, Start Date, End Date, Planned Week
 */

const BASE_URL = 'http://localhost:3000';

// Generate unique test credentials
const timestamp = Date.now();
const TEST_CREDENTIALS = {
  username: `testuser${timestamp}`,
  email: `test${timestamp}@example.com`,
  password: 'testpassword123',
  first_name: 'Test',
  last_name: 'User'
};

async function testNewUserDefaults() {
  console.log('🧪 Testing New User Default Custom Fields...\n');

  try {
    // Step 1: Register a new user
    console.log('👤 Step 1: Registering new user...');
    console.log('Credentials:', {
      username: TEST_CREDENTIALS.username,
      email: TEST_CREDENTIALS.email
    });

    const registerResponse = await fetch(`${BASE_URL}/api/users/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_CREDENTIALS)
    });

    if (!registerResponse.ok) {
      const error = await registerResponse.json();
      console.log('❌ Registration failed:', error.message);
      return;
    }

    const registerData = await registerResponse.json();
    console.log('✅ User registered successfully');

    // Step 2: Login to get token
    console.log('\n🔐 Step 2: Logging in to get token...');
    const loginResponse = await fetch(`${BASE_URL}/api/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: TEST_CREDENTIALS.email,
        password: TEST_CREDENTIALS.password
      })
    });

    if (!loginResponse.ok) {
      const error = await loginResponse.json();
      console.log('❌ Login failed:', error.message);
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Login successful');

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    // Step 3: Get custom field definitions
    console.log('\n📋 Step 3: Fetching custom field definitions...');
    const customFieldsResponse = await fetch(`${BASE_URL}/api/workitems/custom-fields/`, {
      method: 'GET',
      headers
    });

    if (!customFieldsResponse.ok) {
      const error = await customFieldsResponse.json();
      console.log('❌ Failed to fetch custom fields:', error.message);
      return;
    }

    const customFields = await customFieldsResponse.json();
    console.log(`✅ Retrieved ${customFields.length} custom field definitions`);

    // Step 4: Verify all 5 expected fields exist
    console.log('\n🔍 Step 4: Verifying default custom fields...');
    
    const expectedFields = [
      { name: 'Priority', field_type: 'SINGLE_SELECT', sort_order: 0 },
      { name: 'Status', field_type: 'SINGLE_SELECT', sort_order: 1 },
      { name: 'Start Date', field_type: 'DATE', sort_order: 2 },
      { name: 'End Date', field_type: 'DATE', sort_order: 3 },
      { name: 'Planned Week', field_type: 'WEEK', sort_order: 4 }
    ];

    let allFieldsFound = true;
    
    for (const expectedField of expectedFields) {
      const foundField = customFields.find(field => field.name === expectedField.name);
      
      if (!foundField) {
        console.log(`❌ Missing field: ${expectedField.name}`);
        allFieldsFound = false;
      } else if (foundField.field_type !== expectedField.field_type) {
        console.log(`❌ Wrong field type for ${expectedField.name}: expected ${expectedField.field_type}, got ${foundField.field_type}`);
        allFieldsFound = false;
      } else if (foundField.sort_order !== expectedField.sort_order) {
        console.log(`❌ Wrong sort order for ${expectedField.name}: expected ${expectedField.sort_order}, got ${foundField.sort_order}`);
        allFieldsFound = false;
      } else {
        console.log(`✅ ${expectedField.name} (${expectedField.field_type}) - Sort Order: ${foundField.sort_order}`);
        
        // For SINGLE_SELECT fields, also check choice options
        if (expectedField.field_type === 'SINGLE_SELECT' && foundField.choice_options) {
          console.log(`   └─ Choice Options: ${foundField.choice_options.length} options`);
          foundField.choice_options.forEach((option, index) => {
            const defaultIndicator = option.is_default ? ' (DEFAULT)' : '';
            console.log(`      ${index + 1}. ${option.value}${defaultIndicator}`);
          });
        }
      }
    }

    // Step 5: Test field functionality by creating a project
    if (allFieldsFound) {
      console.log('\n🚀 Step 5: Testing field functionality...');
      
      // First, get life aspects to create a project
      const lifeAspectsResponse = await fetch(`${BASE_URL}/api/workitems/life-aspects/`, {
        method: 'GET',
        headers
      });

      if (lifeAspectsResponse.ok) {
        const lifeAspects = await lifeAspectsResponse.json();
        
        if (lifeAspects.length > 0) {
          const testProject = {
            name: 'Test Project for Custom Fields',
            description: 'Testing default custom fields functionality',
            life_aspect: lifeAspects[0].id,
            custom_field_inputs: [
              {
                definition_id: customFields.find(f => f.name === 'Start Date').id,
                value: '2025-06-17'
              },
              {
                definition_id: customFields.find(f => f.name === 'End Date').id,
                value: '2025-06-30'
              },
              {
                definition_id: customFields.find(f => f.name === 'Planned Week').id,
                value: '2025-W25'
              }
            ]
          };

          const createProjectResponse = await fetch(`${BASE_URL}/api/workitems/projects`, {
            method: 'POST',
            headers,
            body: JSON.stringify(testProject)
          });

          if (createProjectResponse.ok) {
            const createdProject = await createProjectResponse.json();
            console.log('✅ Successfully created test project with custom field values');
            console.log('   Project ID:', createdProject.id);
            
            // Verify the custom field values were stored correctly
            if (createdProject.resolved_custom_fields) {
              console.log('   Custom Field Values:');
              createdProject.resolved_custom_fields.forEach(field => {
                console.log(`      ${field.definition.name}: ${field.display_value || field.value || 'null'}`);
              });
            }
          } else {
            const error = await createProjectResponse.json();
            console.log('❌ Failed to create test project:', error.message);
          }
        } else {
          console.log('⚠️ No life aspects found, skipping project creation test');
        }
      } else {
        console.log('⚠️ Failed to fetch life aspects, skipping project creation test');
      }
    }

    // Summary
    console.log('\n📊 Summary:');
    if (allFieldsFound) {
      console.log('🎉 SUCCESS: All 5 default custom fields are present and correctly configured!');
      console.log('   - Priority (SINGLE_SELECT) with 4 choice options');
      console.log('   - Status (SINGLE_SELECT) with 4 choice options and default value');
      console.log('   - Start Date (DATE) field');
      console.log('   - End Date (DATE) field');
      console.log('   - Planned Week (WEEK) field');
    } else {
      console.log('❌ FAILURE: Some default custom fields are missing or incorrectly configured');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
console.log(`
📋 New User Default Custom Fields Test

This test will:
1. Register a new user with unique credentials
2. Login to get authentication token
3. Fetch custom field definitions
4. Verify all 5 expected default fields exist:
   - Priority (SINGLE_SELECT)
   - Status (SINGLE_SELECT) 
   - Start Date (DATE)
   - End Date (DATE)
   - Planned Week (WEEK)
5. Test functionality by creating a project with custom field values

Starting test...
`);

testNewUserDefaults();
