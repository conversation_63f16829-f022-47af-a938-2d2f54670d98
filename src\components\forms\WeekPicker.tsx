"use client";

import React, { useState, useEffect, useRef } from "react";
import { TextField, Popover, Box, Typography, IconButton, Paper, useTheme, Select, MenuItem, FormControl, ButtonBase } from "@mui/material";
import { ChevronLeft, ChevronRight, Clear as ClearIcon, KeyboardArrowDown } from "@mui/icons-material";
import {
  format,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  getWeek,
  addMonths,
  subMonths,
  isSameWeek,
  isToday,
  parse,
  isValid,
} from "date-fns";
import { userPreferencesService } from "@/lib/api/userPreferencesService";

interface WeekPickerProps {
  label?: string;
  value?: string | null; // YYYY-WNN format
  onChange: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  size?: "small" | "medium";
  sx?: any;
}

const WeekPicker: React.FC<WeekPickerProps> = ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  error = false,
  helperText,
  fullWidth = true,
  size = "small",
  sx,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [weekStartsOn, setWeekStartsOn] = useState<0 | 1>(0); // 0 = Sunday, 1 = Monday
  const [hoveredWeek, setHoveredWeek] = useState<Date | null>(null);
  const [showMonthYearSelect, setShowMonthYearSelect] = useState(false);
  const textFieldRef = useRef<HTMLInputElement>(null);

  // Fetch user preference for week start day
  useEffect(() => {
    const fetchWeekStartsOn = async () => {
      try {
        const preferences = await userPreferencesService.getPreferences();
        setWeekStartsOn(preferences.week_starts_on === "Monday" ? 1 : 0);
      } catch (error) {
        console.warn("Failed to fetch week starts on preference:", error);
        // Default to Sunday if fetch fails
        setWeekStartsOn(0);
      }
    };

    fetchWeekStartsOn();
  }, []);

  // Parse value to get selected week
  const selectedWeek = React.useMemo(() => {
    if (!value) return null;

    // Parse YYYY-WNN format
    const match = value.match(/^(\d{4})-W(\d{2})$/);
    if (!match) return null;

    const year = parseInt(match[1]);
    const weekNumber = parseInt(match[2]);

    // Create a date for the first day of the year
    const firstDayOfYear = new Date(year, 0, 1);

    // Find the first week of the year
    const firstWeek = startOfWeek(firstDayOfYear, { weekStartsOn });

    // Calculate the target week
    const targetWeek = new Date(firstWeek.getTime() + (weekNumber - 1) * 7 * 24 * 60 * 60 * 1000);

    return targetWeek;
  }, [value, weekStartsOn]);

  // Format selected week for display
  const displayValue = React.useMemo(() => {
    if (!selectedWeek) return "";

    const weekStart = startOfWeek(selectedWeek, { weekStartsOn });
    const weekEnd = endOfWeek(selectedWeek, { weekStartsOn });
    const weekNumber = getWeek(selectedWeek, { weekStartsOn });

    return `W${weekNumber}: ${format(weekStart, "d MMM")} - ${format(weekEnd, "d MMM")}`;
  }, [selectedWeek, weekStartsOn]);

  const handleTextFieldClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!disabled) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
    setHoveredWeek(null);
  };

  const handleClear = (event: React.MouseEvent) => {
    event.stopPropagation();
    onChange(null);
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const handleMonthYearClick = () => {
    setShowMonthYearSelect(!showMonthYearSelect);
  };

  const handleMonthChange = (month: number) => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), month, 1));
    setShowMonthYearSelect(false);
  };

  const handleYearChange = (year: number) => {
    setCurrentMonth(new Date(year, currentMonth.getMonth(), 1));
    setShowMonthYearSelect(false);
  };

  const handleWeekSelect = (date: Date) => {
    const weekNumber = getWeek(date, { weekStartsOn });
    const year = date.getFullYear();
    const weekValue = `${year}-W${weekNumber.toString().padStart(2, "0")}`;
    onChange(weekValue);
    handleClose();
  };

  const handleDayHover = (date: Date) => {
    setHoveredWeek(date);
  };

  const handleDayLeave = () => {
    setHoveredWeek(null);
  };

  // Generate calendar days
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const calendarStart = startOfWeek(monthStart, { weekStartsOn });
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn });

  const calendarDays = eachDayOfInterval({
    start: calendarStart,
    end: calendarEnd,
  });

  // Group days by weeks
  const weeks: Date[][] = [];
  for (let i = 0; i < calendarDays.length; i += 7) {
    weeks.push(calendarDays.slice(i, i + 7));
  }

  const open = Boolean(anchorEl);

  return (
    <>
      <TextField
        ref={textFieldRef}
        label={label}
        value={displayValue}
        onClick={handleTextFieldClick}
        disabled={disabled}
        required={required}
        error={error}
        helperText={helperText}
        fullWidth={fullWidth}
        size={size}
        sx={sx}
        placeholder="W25: 15 Jun - 21 Jun"
        InputProps={{
          readOnly: true,
          endAdornment:
            value && !disabled ? (
              <IconButton size="small" onClick={handleClear} edge="end" sx={{ mr: -0.5 }}>
                <ClearIcon fontSize="small" />
              </IconButton>
            ) : null,
        }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <Paper sx={{ p: 2, minWidth: 320, maxWidth: 320 }}>
          {/* Header with Month/Year Dropdown and Navigation */}
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2 }}>
            <ButtonBase
              onClick={handleMonthYearClick}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 0.5,
                px: 1,
                py: 0.5,
                borderRadius: 1,
                "&:hover": {
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 500, fontSize: "1rem" }}>
                {format(currentMonth, "MMMM yyyy")}
              </Typography>
              <KeyboardArrowDown fontSize="small" />
            </ButtonBase>

            <Box sx={{ display: "flex", gap: 0.5 }}>
              <IconButton size="small" onClick={handlePreviousMonth}>
                <ChevronLeft fontSize="small" />
              </IconButton>
              <IconButton size="small" onClick={handleNextMonth}>
                <ChevronRight fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          {/* Calendar Header */}
          <Box sx={{ display: "flex", mb: 1 }}>
            {/* Week number column header */}
            <Box sx={{ width: 40, display: "flex", alignItems: "center", justifyContent: "center" }}>
              <Typography
                variant="caption"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.text.disabled,
                  fontSize: "0.75rem",
                }}
              >
                W
              </Typography>
            </Box>
            {/* Day headers */}
            {["S", "M", "T", "W", "T", "F", "S"].map((day, index) => {
              // Adjust day headers based on week start preference
              const adjustedIndex = weekStartsOn === 1 ? (index + 1) % 7 : index;
              const dayHeaders = ["S", "M", "T", "W", "T", "F", "S"];
              return (
                <Box
                  key={index}
                  sx={{
                    flex: 1,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: 32,
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.secondary,
                      fontSize: "0.75rem",
                    }}
                  >
                    {dayHeaders[adjustedIndex]}
                  </Typography>
                </Box>
              );
            })}
          </Box>

          {/* Calendar Grid */}
          {weeks.map((week, weekIndex) => {
            const weekStart = week[0];
            const weekNumber = getWeek(weekStart, { weekStartsOn });
            const isCurrentWeek = week.some((day) => isToday(day));
            const isSelectedWeek = selectedWeek && isSameWeek(weekStart, selectedWeek, { weekStartsOn });
            const isHoveredWeek = hoveredWeek && isSameWeek(weekStart, hoveredWeek, { weekStartsOn });

            return (
              <Box key={weekIndex} sx={{ display: "flex", mb: 0.5 }}>
                {/* Week Number */}
                <Box
                  sx={{
                    width: 40,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: 36,
                    fontSize: "0.75rem",
                    color: theme.palette.text.disabled,
                    fontWeight: 500,
                  }}
                >
                  {weekNumber}
                </Box>

                {/* Week Days */}
                {week.map((day, dayIndex) => {
                  const isOutsideMonth = day.getMonth() !== currentMonth.getMonth();
                  const isDayToday = isToday(day);

                  return (
                    <Box
                      key={dayIndex}
                      onClick={() => handleWeekSelect(day)}
                      onMouseEnter={() => handleDayHover(day)}
                      onMouseLeave={handleDayLeave}
                      sx={{
                        flex: 1,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        height: 36,
                        cursor: "pointer",
                        position: "relative",
                        fontSize: "0.875rem",
                        fontWeight: 400,
                        // Dynamic color based on selection state
                        color: isSelectedWeek
                          ? theme.palette.primary.contrastText
                          : isOutsideMonth
                          ? theme.palette.text.disabled
                          : theme.palette.text.primary,

                        // Week-based styling (entire row)
                        backgroundColor: isSelectedWeek
                          ? theme.palette.primary.main + "20" // 20% opacity
                          : isHoveredWeek
                          ? theme.palette.action.hover
                          : "transparent",

                        "&:hover": {
                          backgroundColor: isSelectedWeek ? theme.palette.primary.main + "30" : theme.palette.action.hover,
                        },

                        // Individual day styling (circular indicators)
                        "&::before": isDayToday
                          ? {
                              content: '""',
                              position: "absolute",
                              width: 32,
                              height: 32,
                              borderRadius: "50%",
                              border: `2px solid ${theme.palette.primary.main}`,
                              zIndex: 1,
                            }
                          : undefined,

                        "&::after": isSelectedWeek
                          ? {
                              content: '""',
                              position: "absolute",
                              width: 32,
                              height: 32,
                              borderRadius: "50%",
                              backgroundColor: theme.palette.primary.main,
                              zIndex: 0,
                            }
                          : undefined,

                        // Ensure text is above the circular background
                        zIndex: 2,
                        position: "relative",
                      }}
                    >
                      {format(day, "d")}
                    </Box>
                  );
                })}
              </Box>
            );
          })}
        </Paper>
      </Popover>
    </>
  );
};

export default WeekPicker;
