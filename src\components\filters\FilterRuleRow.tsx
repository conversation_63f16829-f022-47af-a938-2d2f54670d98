"use client";

import React from "react";
import { Box, Select, MenuItem, FormControl, TextField, IconButton, Typography, Chip } from "@mui/material";
import { Delete as DeleteIcon } from "@mui/icons-material";
import { FilterRule, FIELD_CONDITIONS } from "@/lib/types/filters";
import { CustomFieldDefinition } from "@/lib/types/customFields";
import DatePicker from "@/components/common/DatePicker";
import WeekPicker from "@/components/forms/WeekPicker";

interface FilterRuleRowProps {
  filter: FilterRule;
  customFields: CustomFieldDefinition[];
  onUpdate: (updatedFilter: Partial<FilterRule>) => void;
  onRemove: () => void;
  isFirst: boolean;
}

const FilterRuleRow: React.FC<FilterRuleRowProps> = ({ filter, customFields, onUpdate, onRemove, isFirst }) => {
  const handleFieldChange = (fieldId: string) => {
    const selectedField = customFields.find((field) => field.id === fieldId);
    if (selectedField) {
      onUpdate({
        fieldId,
        fieldName: selectedField.name,
        fieldType: selectedField.field_type,
        condition: "", // Reset condition when field changes
        value: null, // Reset value when field changes
      });
    }
  };

  const handleConditionChange = (condition: string) => {
    onUpdate({
      condition,
      value: null, // Reset value when condition changes
    });
  };

  const handleValueChange = (value: any) => {
    onUpdate({ value });
  };

  const renderValueInput = () => {
    if (!filter.fieldType || !filter.condition) {
      return <TextField placeholder="Select field and condition first" disabled size="small" sx={{ minWidth: 200 }} />;
    }

    switch (filter.fieldType) {
      case "SINGLE_SELECT":
        const selectedField = customFields.find((field) => field.id === filter.fieldId);
        const choiceOptions = selectedField?.choice_options || [];

        return (
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <Select value={filter.value || ""} onChange={(e) => handleValueChange(e.target.value)} displayEmpty>
              <MenuItem value="">
                <em>Select option</em>
              </MenuItem>
              {choiceOptions.map((option) => (
                <MenuItem key={option.id} value={option.id}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    {option.color && (
                      <Box
                        sx={{
                          width: 12,
                          height: 12,
                          borderRadius: "50%",
                          backgroundColor: option.color,
                        }}
                      />
                    )}
                    {option.value}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case "DATE":
        return <DatePicker value={filter.value} onChange={handleValueChange} size="small" sx={{ minWidth: 200 }} />;

      case "WEEK":
        return <WeekPicker value={filter.value} onChange={handleValueChange} size="small" sx={{ minWidth: 200 }} />;

      case "TEXT":
        return (
          <TextField
            value={filter.value || ""}
            onChange={(e) => handleValueChange(e.target.value)}
            placeholder="Enter text"
            size="small"
            sx={{ minWidth: 200 }}
          />
        );

      case "NUMBER":
        return (
          <TextField
            type="number"
            value={filter.value || ""}
            onChange={(e) => handleValueChange(Number(e.target.value))}
            placeholder="Enter number"
            size="small"
            sx={{ minWidth: 200 }}
          />
        );

      default:
        return (
          <TextField
            value={filter.value || ""}
            onChange={(e) => handleValueChange(e.target.value)}
            placeholder="Enter value"
            size="small"
            sx={{ minWidth: 200 }}
          />
        );
    }
  };

  const availableConditions = filter.fieldType ? FIELD_CONDITIONS[filter.fieldType] || [] : [];

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 2, py: 1 }}>
      {/* AND Logic Indicator */}
      {!isFirst && (
        <Chip
          label="AND"
          size="small"
          variant="outlined"
          sx={{
            fontSize: "0.75rem",
            fontWeight: 500,
            color: "text.secondary",
            borderColor: "divider",
          }}
        />
      )}

      {/* Property Select */}
      <FormControl size="small" sx={{ minWidth: 180 }}>
        <Select value={filter.fieldId || ""} onChange={(e) => handleFieldChange(e.target.value)} displayEmpty>
          <MenuItem value="">
            <em>Select property</em>
          </MenuItem>
          {customFields.map((field) => (
            <MenuItem key={field.id} value={field.id}>
              {field.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Condition Select */}
      <FormControl size="small" sx={{ minWidth: 140 }}>
        <Select value={filter.condition || ""} onChange={(e) => handleConditionChange(e.target.value)} disabled={!filter.fieldId} displayEmpty>
          <MenuItem value="">
            <em>Select condition</em>
          </MenuItem>
          {availableConditions.map((condition) => (
            <MenuItem key={condition.value} value={condition.value}>
              {condition.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Value Input */}
      {renderValueInput()}

      {/* Remove Button */}
      <IconButton
        onClick={onRemove}
        size="small"
        sx={{
          color: "text.secondary",
          "&:hover": {
            color: "error.main",
            backgroundColor: "error.light",
          },
        }}
      >
        <DeleteIcon fontSize="small" />
      </IconButton>
    </Box>
  );
};

export default FilterRuleRow;
